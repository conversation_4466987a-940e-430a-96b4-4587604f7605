<?php
// Simple admin panel to view form submissions
$password = 'appi2024'; // Change this password for security

session_start();

// Handle login
if ($_POST && isset($_POST['password'])) {
    if ($_POST['password'] === $password) {
        $_SESSION['admin_logged_in'] = true;
    } else {
        $error = 'Contraseña incorrecta';
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: admin.php');
    exit;
}

// Check if logged in
$logged_in = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'];

function readCSV($filename) {
    if (!file_exists($filename)) {
        return [];
    }
    
    $data = [];
    if (($handle = fopen($filename, "r")) !== FALSE) {
        while (($row = fgetcsv($handle, 1000, ",")) !== FALSE) {
            $data[] = $row;
        }
        fclose($handle);
    }
    return $data;
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Administración - APPI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        :root {
            --paraguay-red: #D52B1E;
            --paraguay-blue: #0038A8;
        }
        .bg-paraguay-red { background-color: var(--paraguay-red); }
        .bg-paraguay-blue { background-color: var(--paraguay-blue); }
        .text-paraguay-red { color: var(--paraguay-red); }
        .text-paraguay-blue { color: var(--paraguay-blue); }
    </style>
</head>
<body class="bg-gray-100">

<?php if (!$logged_in): ?>
    <!-- Login Form -->
    <div class="min-h-screen flex items-center justify-center">
        <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
            <h1 class="text-2xl font-bold text-paraguay-blue mb-6 text-center">Panel de Administración APPI</h1>
            
            <?php if (isset($error)): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="mb-4">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Contraseña</label>
                    <input type="password" id="password" name="password" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-paraguay-red">
                </div>
                <button type="submit" 
                        class="w-full bg-paraguay-red text-white py-2 px-4 rounded-md hover:bg-red-700 transition duration-300">
                    Ingresar
                </button>
            </form>
        </div>
    </div>

<?php else: ?>
    <!-- Admin Dashboard -->
    <div class="min-h-screen bg-gray-100">
        <nav class="bg-paraguay-blue text-white p-4">
            <div class="container mx-auto flex justify-between items-center">
                <h1 class="text-xl font-bold">Panel de Administración APPI</h1>
                <a href="?logout=1" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded transition duration-300">
                    Cerrar Sesión
                </a>
            </div>
        </nav>

        <div class="container mx-auto p-6">
            <!-- Contacts Section -->
            <div class="bg-white rounded-lg shadow-lg mb-8">
                <div class="bg-paraguay-red text-white p-4 rounded-t-lg">
                    <h2 class="text-xl font-bold">Mensajes de Contacto</h2>
                </div>
                <div class="p-4 overflow-x-auto">
                    <?php 
                    $contacts = readCSV('contacts.csv');
                    if (count($contacts) > 1): // More than just header
                    ?>
                        <table class="w-full table-auto">
                            <thead>
                                <tr class="bg-gray-100">
                                    <?php foreach ($contacts[0] as $header): ?>
                                        <th class="px-4 py-2 text-left"><?php echo htmlspecialchars($header); ?></th>
                                    <?php endforeach; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php for ($i = 1; $i < count($contacts); $i++): ?>
                                    <tr class="border-b">
                                        <?php foreach ($contacts[$i] as $cell): ?>
                                            <td class="px-4 py-2"><?php echo htmlspecialchars($cell); ?></td>
                                        <?php endforeach; ?>
                                    </tr>
                                <?php endfor; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <p class="text-gray-500">No hay mensajes de contacto aún.</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Associations Section -->
            <div class="bg-white rounded-lg shadow-lg">
                <div class="bg-paraguay-blue text-white p-4 rounded-t-lg">
                    <h2 class="text-xl font-bold">Solicitudes de Asociación</h2>
                </div>
                <div class="p-4 overflow-x-auto">
                    <?php 
                    $associations = readCSV('associations.csv');
                    if (count($associations) > 1): // More than just header
                    ?>
                        <table class="w-full table-auto">
                            <thead>
                                <tr class="bg-gray-100">
                                    <?php foreach ($associations[0] as $header): ?>
                                        <th class="px-4 py-2 text-left"><?php echo htmlspecialchars($header); ?></th>
                                    <?php endforeach; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php for ($i = 1; $i < count($associations); $i++): ?>
                                    <tr class="border-b">
                                        <?php foreach ($associations[$i] as $cell): ?>
                                            <td class="px-4 py-2"><?php echo htmlspecialchars($cell); ?></td>
                                        <?php endforeach; ?>
                                    </tr>
                                <?php endfor; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <p class="text-gray-500">No hay solicitudes de asociación aún.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

</body>
</html>
