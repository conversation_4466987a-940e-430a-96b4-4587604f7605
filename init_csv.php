<?php
// Initialize CSV files with headers if they don't exist

// Initialize contacts.csv
if (!file_exists('contacts.csv')) {
    $contactsHeader = ['<PERSON><PERSON>', 'Tipo', 'Nombre', 'Email', 'Teléfono', 'Empresa', 'Mensaje'];
    $file = fopen('contacts.csv', 'w');
    fputcsv($file, $contactsHeader);
    fclose($file);
    echo "contacts.csv initialized\n";
}

// Initialize associations.csv
if (!file_exists('associations.csv')) {
    $associationsHeader = ['Fecha', 'Tipo', 'Nombre', 'Email', 'Teléfono', 'Empresa/Parque', 'Tipo de Parque', 'Ubicación', 'Descripción'];
    $file = fopen('associations.csv', 'w');
    fputcsv($file, $associationsHeader);
    fclose($file);
    echo "associations.csv initialized\n";
}

echo "CSV files ready!\n";
?>
